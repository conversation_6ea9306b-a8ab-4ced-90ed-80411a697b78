import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./category-detail.slice";
import { handleApiError } from "@/utils/error-handler";
import qs from "qs";
import { PayloadAction } from "@reduxjs/toolkit";

function* handleFetchCategoryDetail(
  action: PayloadAction<{ slug: string }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));
    yield put(actions.setError(null));
    
    const { slug } = action.payload;

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Make API call
    const response: any = yield call(
      fetch,
      `/api/categories/${slug}/detail`,
      {
        headers,
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        yield put(actions.setLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const result = yield response.json();

    if (!result) {
      yield put(actions.setError("Category not found"));
      return;
    }

    yield put(actions.setCategoryDetail(result));
  } catch (error: any) {
    console.error("Error fetching category detail:", error);
    yield put(actions.setError(error.message || "Failed to fetch category detail"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

function* handleFetchCategoryPrograms(
  action: PayloadAction<{
    slug: string;
    search?: string;
    sort?: { field: string; order: "asc" | "desc" };
    pagination?: { page: number; pageSize: number };
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setProgramsLoading(true));
    yield put(actions.setProgramsError(null));
    
    const { slug, search, sort, pagination } = action.payload;

    // Update local state
    if (search !== undefined) {
      yield put(actions.setProgramsSearch(search));
    }
    if (sort !== undefined) {
      yield put(actions.setProgramsSort(sort));
    }

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Build query parameters
    const queryParams: any = {};
    if (search) {
      queryParams.search = search;
    }
    if (sort) {
      queryParams.sort = `${sort.field}:${sort.order}`;
    }
    if (pagination) {
      queryParams.pagination = pagination;
    }

    const queryString = qs.stringify(queryParams, { encodeValuesOnly: true });

    // Make API call
    const response: any = yield call(
      fetch,
      `/api/categories/${slug}/programs${queryString ? `?${queryString}` : ""}`,
      {
        headers,
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        yield put(actions.setProgramsLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setProgramsError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const result = yield response.json();

    if (!result.data || !result.meta) {
      yield put(actions.setProgramsError("Invalid response structure"));
      return;
    }

    yield put(actions.setCategoryPrograms({ data: result.data, meta: result.meta }));
  } catch (error: any) {
    console.error("Error fetching category programs:", error);
    yield put(actions.setProgramsError(error.message || "Failed to fetch category programs"));
  } finally {
    yield put(actions.setProgramsLoading(false));
  }
}

function* handleFetchCategorySocialContent(
  action: PayloadAction<{
    slug: string;
    platforms?: string[];
    sort?: { field: string; order: "asc" | "desc" };
    pagination?: { page: number; pageSize: number };
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setSocialContentLoading(true));
    yield put(actions.setSocialContentError(null));
    
    const { slug, platforms, sort, pagination } = action.payload;

    // Update local state
    if (platforms !== undefined) {
      yield put(actions.setSocialContentPlatforms(platforms));
    }
    if (sort !== undefined) {
      yield put(actions.setSocialContentSort(sort));
    }

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Build query parameters
    const queryParams: any = {};
    if (platforms && platforms.length > 0) {
      queryParams.platforms = platforms;
    }
    if (sort) {
      queryParams.sort = `${sort.field}:${sort.order}`;
    }
    if (pagination) {
      queryParams.pagination = pagination;
    }

    const queryString = qs.stringify(queryParams, { encodeValuesOnly: true });

    // Make API call
    const response: any = yield call(
      fetch,
      `/api/categories/${slug}/social-content${queryString ? `?${queryString}` : ""}`,
      {
        headers,
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        yield put(actions.setSocialContentLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setSocialContentError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const result = yield response.json();

    if (!result.data || !result.meta) {
      yield put(actions.setSocialContentError("Invalid response structure"));
      return;
    }

    yield put(actions.setCategorySocialContent({ data: result.data, meta: result.meta }));
  } catch (error: any) {
    console.error("Error fetching category social content:", error);
    yield put(actions.setSocialContentError(error.message || "Failed to fetch category social content"));
  } finally {
    yield put(actions.setSocialContentLoading(false));
  }
}

export default function* categoryDetailSaga() {
  yield takeEvery(actions.fetchCategoryDetail.type, handleFetchCategoryDetail);
  yield takeEvery(actions.fetchCategoryPrograms.type, handleFetchCategoryPrograms);
  yield takeEvery(actions.fetchCategorySocialContent.type, handleFetchCategorySocialContent);
}
