import { ICategoryOverview, IAffiliate, IVideo } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

export interface IPagination {
  page: number;
  pageSize: number;
  pageCount?: number;
  total?: number;
}

export interface ISort {
  field: string;
  order: "asc" | "desc";
}

interface CategoryDetailState {
  category: ICategoryOverview | null;
  programs: {
    data: IAffiliate[] | null;
    meta: IPagination | null;
    loading: boolean;
    error: string | null;
    search: string;
    sort: ISort | null;
  };
  socialContent: {
    data: IVideo[] | null;
    meta: IPagination | null;
    loading: boolean;
    error: string | null;
    platforms: string[];
    sort: ISort | null;
  };
  loading: boolean;
  error: string | null;
}

const initialState: CategoryDetailState = {
  category: null,
  programs: {
    data: null,
    meta: null,
    loading: false,
    error: null,
    search: "",
    sort: { field: "name", order: "asc" },
  },
  socialContent: {
    data: null,
    meta: null,
    loading: false,
    error: null,
    platforms: [],
    sort: { field: "views", order: "desc" },
  },
  loading: false,
  error: null,
};

const categoryDetailSlice = createSlice({
  name: "categoryDetail",
  initialState,
  reducers: {
    // Category actions
    setCategoryDetail: (state, action: PayloadAction<ICategoryOverview | null>) => {
      state.category = action.payload;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Programs actions
    setCategoryPrograms: (state, action: PayloadAction<{ data: IAffiliate[]; meta: IPagination }>) => {
      if (state.programs.data && action.payload.meta.page > 1) {
        // Append for pagination
        state.programs.data = [...state.programs.data, ...action.payload.data];
      } else {
        // Replace for new search/sort
        state.programs.data = action.payload.data;
      }
      state.programs.meta = action.payload.meta;
    },

    setProgramsLoading: (state, action: PayloadAction<boolean>) => {
      state.programs.loading = action.payload;
    },

    setProgramsError: (state, action: PayloadAction<string | null>) => {
      state.programs.error = action.payload;
    },

    setProgramsSearch: (state, action: PayloadAction<string>) => {
      state.programs.search = action.payload;
    },

    setProgramsSort: (state, action: PayloadAction<ISort>) => {
      state.programs.sort = action.payload;
    },

    // Social content actions
    setCategorySocialContent: (state, action: PayloadAction<{ data: IVideo[]; meta: IPagination }>) => {
      if (state.socialContent.data && action.payload.meta.page > 1) {
        // Append for pagination
        state.socialContent.data = [...state.socialContent.data, ...action.payload.data];
      } else {
        // Replace for new filters/sort
        state.socialContent.data = action.payload.data;
      }
      state.socialContent.meta = action.payload.meta;
    },

    setSocialContentLoading: (state, action: PayloadAction<boolean>) => {
      state.socialContent.loading = action.payload;
    },

    setSocialContentError: (state, action: PayloadAction<string | null>) => {
      state.socialContent.error = action.payload;
    },

    setSocialContentPlatforms: (state, action: PayloadAction<string[]>) => {
      state.socialContent.platforms = action.payload;
    },

    setSocialContentSort: (state, action: PayloadAction<ISort>) => {
      state.socialContent.sort = action.payload;
    },

    // Async actions
    fetchCategoryDetail: (
      state,
      action: PayloadAction<{ slug: string }>
    ) => {
      // This will be handled by saga
    },

    fetchCategoryPrograms: (
      state,
      action: PayloadAction<{
        slug: string;
        search?: string;
        sort?: ISort;
        pagination?: IPagination;
      }>
    ) => {
      // This will be handled by saga
    },

    fetchCategorySocialContent: (
      state,
      action: PayloadAction<{
        slug: string;
        platforms?: string[];
        sort?: ISort;
        pagination?: IPagination;
      }>
    ) => {
      // This will be handled by saga
    },

    // Clear state
    clearCategoryDetail: (state) => {
      state.category = null;
      state.programs = initialState.programs;
      state.socialContent = initialState.socialContent;
      state.error = null;
    },
  },
});

export const actions = categoryDetailSlice.actions;

// Selectors
export const selectCategoryDetailState = (state: RootState) => state.categoryDetail;

export const selectCategoryDetail = createSelector(
  [selectCategoryDetailState],
  (state) => state.category
);

export const selectCategoryDetailLoading = createSelector(
  [selectCategoryDetailState],
  (state) => state.loading
);

export const selectCategoryDetailError = createSelector(
  [selectCategoryDetailState],
  (state) => state.error
);

export const selectCategoryPrograms = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.data
);

export const selectCategoryProgramsMeta = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.meta
);

export const selectCategoryProgramsLoading = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.loading
);

export const selectCategoryProgramsError = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.error
);

export const selectCategoryProgramsSearch = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.search
);

export const selectCategoryProgramsSort = createSelector(
  [selectCategoryDetailState],
  (state) => state.programs.sort
);

export const selectCategorySocialContent = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.data
);

export const selectCategorySocialContentMeta = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.meta
);

export const selectCategorySocialContentLoading = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.loading
);

export const selectCategorySocialContentError = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.error
);

export const selectCategorySocialContentPlatforms = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.platforms
);

export const selectCategorySocialContentSort = createSelector(
  [selectCategoryDetailState],
  (state) => state.socialContent.sort
);

export default categoryDetailSlice.reducer;
