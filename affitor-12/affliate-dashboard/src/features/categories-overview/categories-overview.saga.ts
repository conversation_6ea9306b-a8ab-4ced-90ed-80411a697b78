import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./categories-overview.slice";
import { handleApiError } from "@/utils/error-handler";
import qs from "qs";
import { PayloadAction } from "@reduxjs/toolkit";

function* handleFetchCategoriesOverview(
  action: PayloadAction<{
    search?: string;
    sort?: { field: string; order: "asc" | "desc" };
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));
    yield put(actions.setError(null));
    
    const { search, sort } = action.payload;

    // Update local state
    if (search !== undefined) {
      yield put(actions.setSearch(search));
    }
    if (sort !== undefined) {
      yield put(actions.setSort(sort));
    }

    // Get token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    const headers: Record<string, string> = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Build query parameters
    const queryParams: any = {};
    if (search) {
      queryParams.search = search;
    }
    if (sort) {
      queryParams.sort = `${sort.field}:${sort.order}`;
    }

    const queryString = qs.stringify(queryParams, { encodeValuesOnly: true });

    // Make API call
    const response: any = yield call(
      fetch,
      `/api/categories/overview${queryString ? `?${queryString}` : ""}`,
      {
        headers,
      }
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        yield put(actions.setLoading(false));
        return; // Error was handled
      }

      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const result = yield response.json();

    if (!result.data) {
      yield put(actions.setError("Invalid response structure"));
      return;
    }

    yield put(actions.setCategoriesOverview(result.data));
  } catch (error: any) {
    console.error("Error fetching categories overview:", error);
    yield put(actions.setError(error.message || "Failed to fetch categories overview"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* categoriesOverviewSaga() {
  yield takeEvery(actions.fetchCategoriesOverview.type, handleFetchCategoriesOverview);
}
