import { ICategoryOverview } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

export interface IPagination {
  page: number;
  pageSize: number;
  pageCount?: number;
  total?: number;
}

export interface ISort {
  field: string;
  order: "asc" | "desc";
}

interface CategoriesOverviewState {
  list: ICategoryOverview[] | null;
  loading: boolean;
  error: string | null;
  search: string;
  sort: ISort | null;
}

const initialState: CategoriesOverviewState = {
  list: null,
  loading: false,
  error: null,
  search: "",
  sort: null,
};

const categoriesOverviewSlice = createSlice({
  name: "categoriesOverview",
  initialState,
  reducers: {
    // Sync actions
    setCategoriesOverview: (state, action: PayloadAction<ICategoryOverview[] | null>) => {
      state.list = action.payload;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    setSearch: (state, action: PayloadAction<string>) => {
      state.search = action.payload;
    },

    setSort: (state, action: PayloadAction<ISort | null>) => {
      state.sort = action.payload;
    },

    // Async actions
    fetchCategoriesOverview: (
      state,
      action: PayloadAction<{
        search?: string;
        sort?: ISort;
      }>
    ) => {
      // This will be handled by saga
    },

    // Clear state
    clearCategoriesOverview: (state) => {
      state.list = null;
      state.error = null;
      state.search = "";
      state.sort = null;
    },
  },
});

export const actions = categoriesOverviewSlice.actions;

// Selectors
export const selectCategoriesOverviewState = (state: RootState) => state.categoriesOverview;

export const selectCategoriesOverviewList = createSelector(
  [selectCategoriesOverviewState],
  (state) => state.list
);

export const selectCategoriesOverviewLoading = createSelector(
  [selectCategoriesOverviewState],
  (state) => state.loading
);

export const selectCategoriesOverviewError = createSelector(
  [selectCategoriesOverviewState],
  (state) => state.error
);

export const selectCategoriesOverviewSearch = createSelector(
  [selectCategoriesOverviewState],
  (state) => state.search
);

export const selectCategoriesOverviewSort = createSelector(
  [selectCategoriesOverviewState],
  (state) => state.sort
);

export default categoriesOverviewSlice.reducer;
