import React, { useState } from "react";
import { ICategoryOverview, ITopProgram, ITopVideo } from "@/interfaces";
import { SortAscIcon, SortDescIcon, ChevronDownIcon } from "lucide-react";
import { NumberFormat } from "@/components/NumberFormat";
import { EPUFormat, CommissionFormat } from "@/components/CurrencyFormat";

interface CategoriesTableProps {
  categories: ICategoryOverview[];
  onCategoryClick: (category: ICategoryOverview) => void;
  onSortChange: (field: string) => void;
  sortOption: { field: string; order: "asc" | "desc" } | null;
}

const CategoriesTable: React.FC<CategoriesTableProps> = ({
  categories,
  onCategoryClick,
  onSortChange,
  sortOption,
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [hoveredProgram, setHoveredProgram] = useState<string | null>(null);
  const [hoveredVideo, setHoveredVideo] = useState<string | null>(null);
  const [showMoreCategories, setShowMoreCategories] = useState(false);

  const getSortIcon = (field: string) => {
    if (sortOption?.field !== field) return null;
    return sortOption.order === "asc" ? <SortAscIcon size={16} /> : <SortDescIcon size={16} />;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const displayedCategories = showMoreCategories ? categories : categories.slice(0, 8);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Desktop Table View */}
      <div className="hidden lg:block">
        {/* Table Header */}
        <div className="bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-medium text-gray-700">
          <div
            className="col-span-2 flex items-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("name")}
          >
            Category Name
            {getSortIcon("name")}
          </div>
          <div
            className="col-span-1 flex items-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("avgEPU")}
          >
            Avg EPU
            {getSortIcon("avgEPU")}
          </div>
          <div className="col-span-3 text-center">Top 3 Programs</div>
          <div className="col-span-3 text-center">Top 3 Videos</div>
          <div
            className="col-span-1 text-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("totalPrograms")}
          >
            Total Programs
            {getSortIcon("totalPrograms")}
          </div>
          <div
            className="col-span-1 text-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("totalVideos")}
          >
            Total Videos
            {getSortIcon("totalVideos")}
          </div>
          <div
            className="col-span-1 text-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("totalViews")}
          >
            Total Views
            {getSortIcon("totalViews")}
          </div>
          <div
            className="col-span-1 text-center cursor-pointer hover:text-gray-900 transition-colors"
            onClick={() => onSortChange("avgMonthlyTraffic")}
          >
            Avg Monthly Traffic
            {getSortIcon("avgMonthlyTraffic")}
          </div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {displayedCategories.map((category) => (
            <div
              key={category.documentId}
              className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors cursor-pointer"
              onMouseEnter={() => setHoveredCategory(category.documentId)}
              onMouseLeave={() => setHoveredCategory(null)}
              onClick={() => onCategoryClick(category)}
            >
            {/* Category Name */}
            <div className="col-span-2">
              <div className="text-blue-600 font-medium hover:text-blue-800 transition-colors">
                {category.name}
              </div>
            </div>

            {/* Avg EPU */}
            <div className="col-span-1">
              <div className="text-gray-900 font-semibold">
                <EPUFormat value={category.avgEPU} />
              </div>
            </div>

            {/* Top 3 Programs */}
            <div className="col-span-3">
              <div className="flex gap-1 justify-center">
                {category.topPrograms.slice(0, 3).map((program, index) => (
                  <div
                    key={program.id}
                    className="relative"
                    onMouseEnter={() => setHoveredProgram(program.id)}
                    onMouseLeave={() => setHoveredProgram(null)}
                  >
                    <div className="w-8 h-8 rounded-md overflow-hidden bg-gray-100 hover:ring-2 hover:ring-blue-500 transition-all">
                      {program.image ? (
                        <img
                          src={program.image.url}
                          alt={program.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-xs font-bold">
                          {program.name.charAt(0)}
                        </div>
                      )}
                    </div>

                    {/* Program Hover Tooltip */}
                    {hoveredProgram === program.id && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black text-white text-xs rounded-lg p-3 shadow-lg z-20 w-64">
                        <div className="font-medium mb-1">{program.name}</div>
                        <div className="space-y-1">
                          <div>EPU: <EPUFormat value={program.epu} /></div>
                          <div>Commission: {program.commission}</div>
                          <div>Monthly Traffic: <NumberFormat value={program.monthlyTraffic} /></div>
                        </div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                      </div>
                    )}
                  </div>
                ))}
                {category.topPrograms.length === 0 && (
                  <div className="text-gray-400 text-sm">No programs</div>
                )}
              </div>
            </div>

            {/* Top 3 Videos */}
            <div className="col-span-3">
              <div className="flex gap-1 justify-center">
                {category.topVideos.slice(0, 3).map((video, index) => (
                  <div
                    key={video.id}
                    className="relative"
                    onMouseEnter={() => setHoveredVideo(video.id)}
                    onMouseLeave={() => setHoveredVideo(null)}
                  >
                    <div className="w-8 h-8 rounded-md overflow-hidden bg-gray-100 hover:ring-2 hover:ring-blue-500 transition-all">
                      {video.thumbnail ? (
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center text-white text-xs">
                          ▶
                        </div>
                      )}
                    </div>

                    {/* Video Hover Tooltip */}
                    {hoveredVideo === video.id && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black text-white text-xs rounded-lg p-3 shadow-lg z-20 w-64">
                        <div className="font-medium mb-1 line-clamp-2">{video.title}</div>
                        <div className="space-y-1">
                          <div>Platform: {video.platform}</div>
                          <div>Views: <NumberFormat value={video.views} /></div>
                          <div>Affiliate: {video.affiliate}</div>
                        </div>
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                      </div>
                    )}
                  </div>
                ))}
                {category.topVideos.length === 0 && (
                  <div className="text-gray-400 text-sm">No videos</div>
                )}
              </div>
            </div>

            {/* Total Programs */}
            <div className="col-span-1 text-center">
              <div className="text-gray-900 font-medium">
                {category.totalPrograms}
              </div>
            </div>

            {/* Total Videos */}
            <div className="col-span-1 text-center">
              <div className="text-gray-900 font-medium">
                {category.totalVideos}
              </div>
            </div>

            {/* Total Views */}
            <div className="col-span-1 text-center">
              <div className="text-gray-900 font-medium">
                {formatNumber(category.totalViews)}
              </div>
            </div>

            {/* Avg Monthly Traffic */}
            <div className="col-span-1 text-center">
              <div className="text-gray-900 font-medium">
                <NumberFormat value={category.avgMonthlyTraffic} />
              </div>
            </div>
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden divide-y divide-gray-200">
        {displayedCategories.map((category) => (
          <div
            key={`mobile-${category.documentId}`}
            className="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
            onClick={() => onCategoryClick(category)}
          >
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-lg font-semibold text-blue-600">{category.name}</h3>
              <span className="text-sm font-medium text-gray-900">
                <EPUFormat value={category.avgEPU} />
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
              <div>
                <span className="text-gray-500">Programs:</span>
                <span className="ml-1 font-medium">{category.totalPrograms}</span>
              </div>
              <div>
                <span className="text-gray-500">Videos:</span>
                <span className="ml-1 font-medium">{category.totalVideos}</span>
              </div>
              <div>
                <span className="text-gray-500">Views:</span>
                <span className="ml-1 font-medium">{formatNumber(category.totalViews)}</span>
              </div>
              <div>
                <span className="text-gray-500">Traffic:</span>
                <span className="ml-1 font-medium"><NumberFormat value={category.avgMonthlyTraffic} /></span>
              </div>
            </div>

            {/* Top Programs Preview */}
            {category.topPrograms.length > 0 && (
              <div className="flex gap-2 mb-2">
                <span className="text-xs text-gray-500 mr-2">Top Programs:</span>
                {category.topPrograms.slice(0, 3).map((program) => (
                  <div key={program.id} className="w-6 h-6 rounded bg-gray-100 overflow-hidden">
                    {program.image ? (
                      <img src={program.image.url} alt={program.name} className="w-full h-full object-cover" />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-xs font-bold">
                        {program.name.charAt(0)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Show More Button */}
      {categories.length > 8 && (
        <div className="border-t border-gray-200 bg-gray-50">
          <button
            onClick={() => setShowMoreCategories(!showMoreCategories)}
            className="w-full py-4 text-center text-gray-600 hover:text-gray-900 transition-colors flex items-center justify-center gap-2"
          >
            {showMoreCategories ? "Show Less" : `Show ${categories.length - 8} More Categories`}
            <ChevronDownIcon
              size={16}
              className={`transform transition-transform ${showMoreCategories ? "rotate-180" : ""}`}
            />
          </button>
        </div>
      )}
    </div>
  );
};

export default CategoriesTable;
