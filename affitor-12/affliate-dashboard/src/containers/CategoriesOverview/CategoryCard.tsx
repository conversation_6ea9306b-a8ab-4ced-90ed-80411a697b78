import React, { useState } from "react";
import { ICategoryOverview, ITopProgram, ITopVideo } from "@/interfaces";
import { PlayIcon, EyeIcon, TrendingUpIcon, DollarSignIcon } from "lucide-react";
import { NumberFormat } from "@/components/NumberFormat";
import { EPUFormat } from "@/components/CurrencyFormat";

interface CategoryCardProps {
  category: ICategoryOverview;
  onClick: () => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, onClick }) => {
  const [hoveredProgram, setHoveredProgram] = useState<string | null>(null);
  const [hoveredVideo, setHoveredVideo] = useState<string | null>(null);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden cursor-pointer">
      {/* Header */}
      <div className="p-6 border-b border-gray-100" onClick={onClick}>
        <h3 className="text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
          {category.name}
        </h3>
        {category.description && (
          <p className="text-gray-600 text-sm line-clamp-2">{category.description}</p>
        )}
      </div>

      {/* Metrics */}
      <div className="p-6 border-b border-gray-100">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <DollarSignIcon size={16} className="text-green-500 mr-1" />
              <span className="text-lg font-semibold text-gray-900">
                <EPUFormat value={category.avgEPU} />
              </span>
            </div>
            <p className="text-xs text-gray-500">Avg EPU</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <TrendingUpIcon size={16} className="text-blue-500 mr-1" />
              <span className="text-lg font-semibold text-gray-900">
                <NumberFormat value={category.avgMonthlyTraffic} />
              </span>
            </div>
            <p className="text-xs text-gray-500">Avg Monthly Traffic</p>
          </div>
          
          <div className="text-center">
            <span className="text-lg font-semibold text-gray-900">
              {category.totalPrograms}
            </span>
            <p className="text-xs text-gray-500">Programs</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <EyeIcon size={16} className="text-purple-500 mr-1" />
              <span className="text-lg font-semibold text-gray-900">
                {formatNumber(category.totalViews)}
              </span>
            </div>
            <p className="text-xs text-gray-500">Total Views</p>
          </div>
        </div>
      </div>

      {/* Top Programs */}
      {category.topPrograms.length > 0 && (
        <div className="p-6 border-b border-gray-100">
          <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
            <TrendingUpIcon size={14} className="mr-1" />
            Top Programs
          </h4>
          <div className="flex gap-2">
            {category.topPrograms.slice(0, 3).map((program) => (
              <div
                key={program.id}
                className="relative flex-1"
                onMouseEnter={() => setHoveredProgram(program.id)}
                onMouseLeave={() => setHoveredProgram(null)}
              >
                <div className="w-full h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xs font-medium text-gray-600 hover:bg-gray-200 transition-colors">
                  {program.image ? (
                    <img
                      src={program.image.url}
                      alt={program.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <span className="truncate px-2">{program.name}</span>
                  )}
                </div>
                
                {/* Hover Preview */}
                {hoveredProgram === program.id && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black text-white text-xs rounded-lg p-3 shadow-lg z-10 w-64">
                    <div className="font-medium mb-1">{program.name}</div>
                    <div className="space-y-1">
                      <div>EPU: ${program.epu.toFixed(2)}</div>
                      <div>Commission: {program.commission}</div>
                      <div>Monthly Traffic: <NumberFormat value={program.monthlyTraffic} /></div>
                    </div>
                    <button className="mt-2 text-blue-300 hover:text-blue-100 text-xs">
                      View Details →
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Videos */}
      {category.topVideos.length > 0 && (
        <div className="p-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
            <PlayIcon size={14} className="mr-1" />
            Top Videos
          </h4>
          <div className="flex gap-2">
            {category.topVideos.slice(0, 3).map((video) => (
              <div
                key={video.id}
                className="relative flex-1"
                onMouseEnter={() => setHoveredVideo(video.id)}
                onMouseLeave={() => setHoveredVideo(null)}
              >
                <div className="w-full h-16 bg-gray-100 rounded-lg overflow-hidden relative group">
                  {video.thumbnail ? (
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                      No thumbnail
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                    <PlayIcon size={16} className="text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                    {formatNumber(video.views)}
                  </div>
                </div>
                
                {/* Video starts playing on hover - placeholder for now */}
                {hoveredVideo === video.id && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black text-white text-xs rounded-lg p-3 shadow-lg z-10 w-64">
                    <div className="font-medium mb-1 line-clamp-2">{video.title}</div>
                    <div className="space-y-1">
                      <div>Platform: {video.platform}</div>
                      <div>Views: <NumberFormat value={video.views} /></div>
                      <div>Affiliate: {video.affiliate}</div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 text-center">
        <button
          onClick={onClick}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
        >
          View Category Details →
        </button>
      </div>
    </div>
  );
};

export default CategoryCard;
