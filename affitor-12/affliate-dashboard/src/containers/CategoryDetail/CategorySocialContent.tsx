import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/category-detail/category-detail.slice";
import {
  selectCategorySocialContent,
  selectCategorySocialContentMeta,
  selectCategorySocialContentLoading,
  selectCategorySocialContentError,
  selectCategorySocialContentPlatforms,
  selectCategorySocialContentSort,
} from "@/features/category-detail/category-detail.slice";
import { CircularProgress } from "@mui/material";
import { PlayIcon, SortAscIcon, SortDescIcon, FilterIcon } from "lucide-react";
import { CustomButton } from "@/components/CustomButton";
import { NumberFormat } from "@/components/NumberFormat";

interface CategorySocialContentProps {
  categorySlug: string;
}

const CategorySocialContent: React.FC<CategorySocialContentProps> = ({ categorySlug }) => {
  const dispatch = useDispatch();
  
  const socialContent = useSelector(selectCategorySocialContent);
  const meta = useSelector(selectCategorySocialContentMeta);
  const loading = useSelector(selectCategorySocialContentLoading);
  const error = useSelector(selectCategorySocialContentError);
  const selectedPlatforms = useSelector(selectCategorySocialContentPlatforms);
  const sortOption = useSelector(selectCategorySocialContentSort);

  const [showFilters, setShowFilters] = useState(false);

  const platforms = [
    { id: "youtube", name: "YouTube", color: "bg-red-500" },
    { id: "tiktok", name: "TikTok", color: "bg-black" },
    { id: "x", name: "X (Twitter)", color: "bg-blue-500" },
    { id: "reddit", name: "Reddit", color: "bg-orange-500" },
  ];

  // Initial fetch
  useEffect(() => {
    if (categorySlug) {
      dispatch(actions.fetchCategorySocialContent({ 
        slug: categorySlug,
        pagination: { page: 1, pageSize: 12 }
      }));
    }
  }, [categorySlug, dispatch]);

  const handlePlatformToggle = useCallback((platformId: string) => {
    const newPlatforms = selectedPlatforms.includes(platformId)
      ? selectedPlatforms.filter(p => p !== platformId)
      : [...selectedPlatforms, platformId];
    
    dispatch(actions.fetchCategorySocialContent({ 
      slug: categorySlug,
      platforms: newPlatforms,
      sort: sortOption || undefined,
      pagination: { page: 1, pageSize: 12 }
    }));
  }, [selectedPlatforms, sortOption, categorySlug, dispatch]);

  const handleSortChange = useCallback((field: string) => {
    const newOrder = 
      sortOption?.field === field && sortOption?.order === "asc" 
        ? "desc" 
        : "asc";
    
    const newSort = { field, order: newOrder as "asc" | "desc" };
    
    dispatch(actions.fetchCategorySocialContent({ 
      slug: categorySlug,
      platforms: selectedPlatforms.length > 0 ? selectedPlatforms : undefined,
      sort: newSort,
      pagination: { page: 1, pageSize: 12 }
    }));
  }, [sortOption, selectedPlatforms, categorySlug, dispatch]);

  const handleLoadMore = useCallback(() => {
    if (meta && meta.page < meta.pageCount!) {
      dispatch(actions.fetchCategorySocialContent({ 
        slug: categorySlug,
        platforms: selectedPlatforms.length > 0 ? selectedPlatforms : undefined,
        sort: sortOption || undefined,
        pagination: { page: meta.page + 1, pageSize: 12 }
      }));
    }
  }, [meta, selectedPlatforms, sortOption, categorySlug, dispatch]);

  const getSortIcon = (field: string) => {
    if (sortOption?.field !== field) return null;
    return sortOption.order === "asc" ? <SortAscIcon size={16} /> : <SortDescIcon size={16} />;
  };

  const formatDuration = (duration: string) => {
    if (!duration) return "";
    // Convert ISO 8601 duration to readable format
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return duration;
    
    const hours = match[1] ? parseInt(match[1]) : 0;
    const minutes = match[2] ? parseInt(match[2]) : 0;
    const seconds = match[3] ? parseInt(match[3]) : 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <PlayIcon size={24} className="mr-3 text-purple-600" />
          Top Social Content (Video & Ads)
        </h2>
      </div>

      {/* Filters and Sort Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
          {/* Platform Filters */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FilterIcon size={16} />
              Platforms
              {selectedPlatforms.length > 0 && (
                <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                  {selectedPlatforms.length}
                </span>
              )}
            </button>
            
            {showFilters && (
              <div className="flex gap-2 flex-wrap">
                {platforms.map((platform) => (
                  <button
                    key={platform.id}
                    onClick={() => handlePlatformToggle(platform.id)}
                    className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                      selectedPlatforms.includes(platform.id)
                        ? `${platform.color} text-white`
                        : "border border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    {platform.name}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Sort Options */}
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => handleSortChange("views")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Views {getSortIcon("views")}
            </button>
            <button
              onClick={() => handleSortChange("likes")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Likes {getSortIcon("likes")}
            </button>
            <button
              onClick={() => handleSortChange("published_from")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Date {getSortIcon("published_from")}
            </button>
          </div>
        </div>

        {/* Social Content Grid */}
        {error ? (
          <div className="text-center py-8">
            <p className="text-red-500 text-lg mb-4">{error}</p>
            <CustomButton
              onClick={() => dispatch(actions.fetchCategorySocialContent({ 
                slug: categorySlug,
                pagination: { page: 1, pageSize: 12 }
              }))}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Try Again
            </CustomButton>
          </div>
        ) : loading && !socialContent ? (
          <div className="text-center py-12">
            <CircularProgress color="inherit" />
            <p className="mt-4 text-gray-600">Loading social content...</p>
          </div>
        ) : socialContent && socialContent.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {socialContent.map((content) => (
                <div
                  key={content.documentId}
                  className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div className="relative">
                    {content.thumbnail ? (
                      <img
                        src={content.thumbnail}
                        alt={content.title}
                        className="w-full h-40 object-cover"
                      />
                    ) : (
                      <div className="w-full h-40 bg-gray-100 flex items-center justify-center">
                        <PlayIcon size={24} className="text-gray-400" />
                      </div>
                    )}
                    
                    {/* Platform Badge */}
                    <div className={`absolute top-2 left-2 px-2 py-1 text-xs text-white rounded ${
                      platforms.find(p => p.id === content.platform)?.color || "bg-gray-500"
                    }`}>
                      {content.platform}
                    </div>
                    
                    {/* Duration */}
                    {content.duration && (
                      <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        {formatDuration(content.duration)}
                      </div>
                    )}
                    
                    {/* Views */}
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      <NumberFormat value={content.views || 0} />
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h4 className="font-medium text-gray-900 text-sm line-clamp-2 mb-2">
                      {content.title}
                    </h4>
                    <div className="space-y-1 text-xs text-gray-500">
                      <div>By: {content.channel_title}</div>
                      {content.affiliate && (
                        <div>Program: {content.affiliate.name}</div>
                      )}
                      <div className="flex justify-between">
                        <span>Likes: {content.likes || 0}</span>
                        <span>Comments: {content.comments || 0}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Load More Button */}
            {meta && meta.page < meta.pageCount! && (
              <div className="text-center pt-6">
                {loading ? (
                  <CircularProgress color="inherit" size={24} />
                ) : (
                  <CustomButton
                    onClick={handleLoadMore}
                    className="px-6 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors"
                  >
                    Load More Content
                  </CustomButton>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No social content available for this category
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategorySocialContent;
