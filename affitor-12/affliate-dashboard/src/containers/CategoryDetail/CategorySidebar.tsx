import React from "react";
import { BarChart3Icon, UsersIcon, PlayIcon } from "lucide-react";

interface CategorySidebarProps {
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
}

const CategorySidebar: React.FC<CategorySidebarProps> = ({
  activeSection,
  onSectionClick,
}) => {
  const sections = [
    {
      id: "summary",
      label: "Category Summary",
      icon: BarChart3Icon,
      description: "Overview and key metrics"
    },
    {
      id: "programs",
      label: "Program Metrics",
      icon: UsersIcon,
      description: "Detailed program listings"
    },
    {
      id: "socialContent",
      label: "Top Social Content",
      icon: PlayIcon,
      description: "Videos and ads"
    },
  ];

  return (
    <div className="sticky top-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-semibold text-gray-900 mb-4">Page Sections</h3>
        
        <nav className="space-y-2">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => onSectionClick(section.id)}
              className={`w-full text-left p-3 rounded-lg transition-colors ${
                activeSection === section.id
                  ? "bg-blue-50 text-blue-700 border border-blue-200"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
            >
              <div className="flex items-start space-x-3">
                <section.icon 
                  size={18} 
                  className={
                    activeSection === section.id 
                      ? "text-blue-600 mt-0.5" 
                      : "text-gray-400 mt-0.5"
                  } 
                />
                <div className="flex-1 min-w-0">
                  <div className={`font-medium text-sm ${
                    activeSection === section.id ? "text-blue-700" : "text-gray-900"
                  }`}>
                    {section.label}
                  </div>
                  <div className={`text-xs mt-1 ${
                    activeSection === section.id ? "text-blue-600" : "text-gray-500"
                  }`}>
                    {section.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default CategorySidebar;
