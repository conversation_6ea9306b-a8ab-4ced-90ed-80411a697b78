import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/category-detail/category-detail.slice";
import {
  selectCategoryPrograms,
  selectCategoryProgramsMeta,
  selectCategoryProgramsLoading,
  selectCategoryProgramsError,
  selectCategoryProgramsSearch,
  selectCategoryProgramsSort,
} from "@/features/category-detail/category-detail.slice";
import { CircularProgress } from "@mui/material";
import { SearchIcon, SortAscIcon, SortDescIcon, UsersIcon } from "lucide-react";
import { TableAffiliate } from "@/components/TableAffiliate";
import { CustomButton } from "@/components/CustomButton";

interface CategoryProgramsProps {
  categorySlug: string;
}

const CategoryPrograms: React.FC<CategoryProgramsProps> = ({ categorySlug }) => {
  const dispatch = useDispatch();
  
  const programs = useSelector(selectCategoryPrograms);
  const meta = useSelector(selectCategoryProgramsMeta);
  const loading = useSelector(selectCategoryProgramsLoading);
  const error = useSelector(selectCategoryProgramsError);
  const searchTerm = useSelector(selectCategoryProgramsSearch);
  const sortOption = useSelector(selectCategoryProgramsSort);

  const [localSearch, setLocalSearch] = useState(searchTerm);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearch !== searchTerm) {
        dispatch(actions.fetchCategoryPrograms({ 
          slug: categorySlug,
          search: localSearch,
          sort: sortOption || undefined,
          pagination: { page: 1, pageSize: 10 }
        }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localSearch, searchTerm, sortOption, categorySlug, dispatch]);

  // Initial fetch
  useEffect(() => {
    if (categorySlug) {
      dispatch(actions.fetchCategoryPrograms({ 
        slug: categorySlug,
        pagination: { page: 1, pageSize: 10 }
      }));
    }
  }, [categorySlug, dispatch]);

  const handleSortChange = useCallback((field: string) => {
    const newOrder = 
      sortOption?.field === field && sortOption?.order === "asc" 
        ? "desc" 
        : "asc";
    
    const newSort = { field, order: newOrder as "asc" | "desc" };
    
    dispatch(actions.fetchCategoryPrograms({ 
      slug: categorySlug,
      search: searchTerm,
      sort: newSort,
      pagination: { page: 1, pageSize: 10 }
    }));
  }, [sortOption, searchTerm, categorySlug, dispatch]);

  const handleLoadMore = useCallback(() => {
    if (meta && meta.page < meta.pageCount!) {
      dispatch(actions.fetchCategoryPrograms({ 
        slug: categorySlug,
        search: searchTerm,
        sort: sortOption || undefined,
        pagination: { page: meta.page + 1, pageSize: 10 }
      }));
    }
  }, [meta, searchTerm, sortOption, categorySlug, dispatch]);

  const getSortIcon = (field: string) => {
    if (sortOption?.field !== field) return null;
    return sortOption.order === "asc" ? <SortAscIcon size={16} /> : <SortDescIcon size={16} />;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <UsersIcon size={24} className="mr-3 text-blue-600" />
          Program Metrics
        </h2>
      </div>

      {/* Search and Sort Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search programs..."
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Sort Options */}
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => handleSortChange("name")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Name {getSortIcon("name")}
            </button>
            <button
              onClick={() => handleSortChange("monthly_traffic")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Traffic {getSortIcon("monthly_traffic")}
            </button>
            <button
              onClick={() => handleSortChange("totalViews")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Views {getSortIcon("totalViews")}
            </button>
            <button
              onClick={() => handleSortChange("totalVideos")}
              className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Videos {getSortIcon("totalVideos")}
            </button>
          </div>
        </div>

        {/* Programs Table */}
        {error ? (
          <div className="text-center py-8">
            <p className="text-red-500 text-lg mb-4">{error}</p>
            <CustomButton
              onClick={() => dispatch(actions.fetchCategoryPrograms({ 
                slug: categorySlug,
                pagination: { page: 1, pageSize: 10 }
              }))}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Try Again
            </CustomButton>
          </div>
        ) : loading && !programs ? (
          <div className="text-center py-12">
            <CircularProgress color="inherit" />
            <p className="mt-4 text-gray-600">Loading programs...</p>
          </div>
        ) : programs && programs.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <TableAffiliate
                data={programs}
                pageSize={10}
                isPagination={false}
                forceHideColumns={["avg_conversion", "recurring", "pricing", "launch_year", "payment_methods", "cookie_duration", "contact_info", "country"]}
              />
            </div>
            
            {/* Load More Button */}
            {meta && meta.page < meta.pageCount! && (
              <div className="text-center pt-6">
                {loading ? (
                  <CircularProgress color="inherit" size={24} />
                ) : (
                  <CustomButton
                    onClick={handleLoadMore}
                    className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                  >
                    Load More Programs
                  </CustomButton>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              {searchTerm ? "No programs found matching your search" : "No programs available in this category"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPrograms;
