import React from "react";
import { ICategoryOverview } from "@/interfaces";
import { 
  DollarSignIcon, 
  TrendingUpIcon, 
  EyeIcon, 
  PlayIcon,
  UsersIcon,
  BarChart3Icon 
} from "lucide-react";
import { NumberFormat } from "@/components/NumberFormat";

interface CategorySummaryProps {
  category: ICategoryOverview;
}

const CategorySummary: React.FC<CategorySummaryProps> = ({ category }) => {
  const metrics = [
    {
      label: "Average EPU",
      value: `$${category.avgEPU.toFixed(2)}`,
      icon: DollarSignIcon,
      color: "text-green-500",
      bgColor: "bg-green-50",
      description: "Average cost per 1,000 impressions"
    },
    {
      label: "Total Programs",
      value: category.totalPrograms.toString(),
      icon: UsersIcon,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      description: "Number of affiliate programs"
    },
    {
      label: "Total Videos",
      value: category.totalVideos.toString(),
      icon: PlayIcon,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      description: "Aggregated videos from social listening"
    },
    {
      label: "Total Views",
      value: <NumberFormat value={category.totalViews} />,
      icon: EyeIcon,
      color: "text-red-500",
      bgColor: "bg-red-50",
      description: "Total views across all programs"
    },
    {
      label: "Avg Monthly Traffic",
      value: <NumberFormat value={category.avgMonthlyTraffic} />,
      icon: TrendingUpIcon,
      color: "text-orange-500",
      bgColor: "bg-orange-50",
      description: "Estimated average monthly traffic"
    },
  ];

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <BarChart3Icon size={24} className="mr-3 text-blue-600" />
          Category Overview
        </h2>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {metrics.map((metric, index) => (
          <div
            key={index}
            className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${metric.bgColor}`}>
                <metric.icon size={24} className={metric.color} />
              </div>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">{metric.label}</p>
              <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
              <p className="text-xs text-gray-500">{metric.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Top Programs Preview */}
      {category.topPrograms.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUpIcon size={20} className="mr-2 text-blue-600" />
            Top Performing Programs
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {category.topPrograms.slice(0, 3).map((program, index) => (
              <div
                key={program.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-3 mb-3">
                  {program.image ? (
                    <img
                      src={program.image.url}
                      alt={program.name}
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-gray-400 text-xs font-medium">
                        {program.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">
                      {program.name}
                    </h4>
                    <p className="text-sm text-gray-500">#{index + 1} by views</p>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">EPU:</span>
                    <span className="font-medium">${program.epu.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Commission:</span>
                    <span className="font-medium">{program.commission}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly Traffic:</span>
                    <span className="font-medium">
                      <NumberFormat value={program.monthlyTraffic} />
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Videos Preview */}
      {category.topVideos.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <PlayIcon size={20} className="mr-2 text-purple-600" />
            Most Viewed Videos
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {category.topVideos.slice(0, 3).map((video, index) => (
              <div
                key={video.id}
                className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="relative">
                  {video.thumbnail ? (
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-32 object-cover"
                    />
                  ) : (
                    <div className="w-full h-32 bg-gray-100 flex items-center justify-center">
                      <PlayIcon size={24} className="text-gray-400" />
                    </div>
                  )}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    <NumberFormat value={video.views} />
                  </div>
                  <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    #{index + 1}
                  </div>
                </div>
                
                <div className="p-3">
                  <h4 className="font-medium text-gray-900 text-sm line-clamp-2 mb-2">
                    {video.title}
                  </h4>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{video.platform}</span>
                    <span>{video.affiliate}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CategorySummary;
