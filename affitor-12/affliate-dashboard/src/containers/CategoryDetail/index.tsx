import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { actions } from "@/features/category-detail/category-detail.slice";
import {
  selectCategoryDetail,
  selectCategoryDetailLoading,
  selectCategoryDetailError,
} from "@/features/category-detail/category-detail.slice";
import { CircularProgress } from "@mui/material";
import { ArrowLeftIcon } from "lucide-react";
import CategorySummary from "./CategorySummary";
import CategoryPrograms from "./CategoryPrograms";
import CategorySocialContent from "./CategorySocialContent";
import CategorySidebar from "./CategorySidebar";
import { CustomButton } from "@/components/CustomButton";

const CategoryDetail: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { slug } = router.query;
  
  const category = useSelector(selectCategoryDetail);
  const loading = useSelector(selectCategoryDetailLoading);
  const error = useSelector(selectCategoryDetailError);

  const summaryRef = useRef<HTMLDivElement>(null);
  const programsRef = useRef<HTMLDivElement>(null);
  const socialContentRef = useRef<HTMLDivElement>(null);

  const [activeSection, setActiveSection] = useState("summary");

  // Fetch category detail when slug changes
  useEffect(() => {
    if (slug && typeof slug === "string") {
      dispatch(actions.fetchCategoryDetail({ slug }));
    }
  }, [slug, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      dispatch(actions.clearCategoryDetail());
    };
  }, [dispatch]);

  // Handle scroll to section
  const scrollToSection = (sectionId: string) => {
    const refs = {
      summary: summaryRef,
      programs: programsRef,
      socialContent: socialContentRef,
    };
    
    const ref = refs[sectionId as keyof typeof refs];
    if (ref?.current) {
      ref.current.scrollIntoView({ behavior: "smooth" });
      setActiveSection(sectionId);
    }
  };

  // Handle intersection observer for sidebar highlighting
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.getAttribute("data-section");
            if (sectionId) {
              setActiveSection(sectionId);
            }
          }
        });
      },
      { threshold: 0.3 }
    );

    const sections = [summaryRef.current, programsRef.current, socialContentRef.current];
    sections.forEach((section) => {
      if (section) observer.observe(section);
    });

    return () => {
      sections.forEach((section) => {
        if (section) observer.unobserve(section);
      });
    };
  }, []);

  const handleBackClick = () => {
    router.push("/categories");
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="text-center py-12">
          <CircularProgress color="inherit" />
          <p className="mt-4 text-gray-600">Loading category details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-red-500 text-lg mb-4">{error}</p>
          <div className="space-x-4">
            <CustomButton
              onClick={() => slug && dispatch(actions.fetchCategoryDetail({ slug: slug as string }))}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Try Again
            </CustomButton>
            <CustomButton
              onClick={handleBackClick}
              className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Back to Categories
            </CustomButton>
          </div>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-gray-500 text-lg mb-4">Category not found</p>
          <CustomButton
            onClick={handleBackClick}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Back to Categories
          </CustomButton>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={handleBackClick}
          className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
        >
          <ArrowLeftIcon size={20} className="mr-2" />
          Back to Categories
        </button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {category.name}
            </h1>
            {category.description && (
              <p className="text-gray-600 text-lg">{category.description}</p>
            )}
          </div>
        </div>
      </div>

      {/* Main Content with Sidebar */}
      <div className="flex gap-8">
        {/* Sidebar */}
        <div className="hidden lg:block w-64 flex-shrink-0">
          <CategorySidebar
            activeSection={activeSection}
            onSectionClick={scrollToSection}
          />
        </div>

        {/* Content */}
        <div className="flex-1 space-y-12">
          {/* Category Summary Section */}
          <div ref={summaryRef} data-section="summary">
            <CategorySummary category={category} />
          </div>

          {/* Programs Section */}
          <div ref={programsRef} data-section="programs">
            <CategoryPrograms categorySlug={slug as string} />
          </div>

          {/* Social Content Section */}
          <div ref={socialContentRef} data-section="socialContent">
            <CategorySocialContent categorySlug={slug as string} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryDetail;
