import React from "react";
import { GetServerSideProps } from "next";
import Head from "next/head";
import CategoryDetail from "@/containers/CategoryDetail";

export default function DynamicCategoryPage() {
  return (
    <>
      <Head>
        <title>Category Details - Affitor</title>
        <meta
          name="description"
          content="Detailed view of affiliate program category with metrics, programs, and social content."
        />
      </Head>

      <CategoryDetail />
    </>
  );
}

// Optional: Add server-side props if needed for SEO or initial data
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {},
  };
};
