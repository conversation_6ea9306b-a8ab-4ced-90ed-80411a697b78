import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { slug } = req.query;
    
    if (!slug || typeof slug !== 'string') {
      return res.status(400).json({ 
        statusCode: 400, 
        message: "Category slug is required" 
      });
    }

    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Pass token and cookies to StrapiClient
    const response = await StrapiClient.getCategoryDetail(
      slug,
      token || undefined,
      cookies
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching category detail:", error);
    sendApiError(res, error, "Error fetching category detail");
  }
}
