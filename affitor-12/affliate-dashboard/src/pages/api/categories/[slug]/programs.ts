import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import qs from "qs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { slug } = req.query;
    
    if (!slug || typeof slug !== 'string') {
      return res.status(400).json({ 
        statusCode: 400, 
        message: "Category slug is required" 
      });
    }

    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Remove slug from query and serialize the rest
    const { slug: _, ...queryParams } = req.query;
    const queryString = qs.stringify(queryParams, { encodeValuesOnly: true });

    // Pass token and cookies to StrapiClient
    const response = await StrapiClient.getCategoryPrograms(
      slug,
      queryString,
      token || undefined,
      cookies
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching category programs:", error);
    sendApiError(res, error, "Error fetching category programs");
  }
}
