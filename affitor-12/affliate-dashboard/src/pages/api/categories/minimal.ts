import { NextApiRequest, NextApiResponse } from 'next';
import { StrapiClient } from '@/utils/request';
import { sendApiError } from '@/utils/api-error-handler';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const response = await StrapiClient.getCategoriesMinimal();

    res.status(200).json(response);
  } catch (error: any) {
    console.error('Error fetching minimal categories:', error);
    sendApiError(res, error, 'Error fetching minimal categories');
  }
}
