import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import qs from "qs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Serialize the query object into a query string
    const queryString = qs.stringify(req.query, { encodeValuesOnly: true });

    // Pass token and cookies to StrapiClient
    const response = await StrapiClient.getCategoriesOverview(
      queryString,
      token || undefined,
      cookies
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching categories overview:", error);
    sendApiError(res, error, "Error fetching categories overview");
  }
}
