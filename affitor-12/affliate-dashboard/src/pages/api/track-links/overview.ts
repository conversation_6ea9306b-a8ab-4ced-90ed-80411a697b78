import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Use centralized context creation
    const { token } = createApiContext(req);

    const response = await StrapiClient.getTrackLinksOverview(token);

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching track links overview:", error);
    sendApiError(res, error, "Error fetching track links overview");
  }
}
