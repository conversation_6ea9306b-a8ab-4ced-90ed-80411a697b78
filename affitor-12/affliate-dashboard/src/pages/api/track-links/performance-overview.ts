import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Use centralized context creation
    const { token } = createApiContext(req);
    const { period } = req.query;

    const response = await StrapiClient.getTrackLinksPerformanceOverview(
      period as string,
      token
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching track links performance overview:", error);
    sendApiError(res, error, "Error fetching track links performance overview");
  }
}
