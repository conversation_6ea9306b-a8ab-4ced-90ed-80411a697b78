import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "GET") {
    try {
      // Use centralized context creation
      const { token } = createApiContext(req, { requireAuth: true });

      // Call StrapiClient
      const response = await StrapiClient.getPayoutsOverview(token);

      res.status(200).json(response);
    } catch (error: any) {
      console.error("Error fetching payouts overview:", error);
      sendApiError(res, error, "Error fetching payouts overview");
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
