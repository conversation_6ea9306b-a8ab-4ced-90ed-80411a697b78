import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import qs from "qs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "GET") {
    try {
      // Use centralized context creation
      const { token } = createApiContext(req, { requireAuth: true });

      const { type, ...queryParams } = req.query;

      // Handle different types of requests
      if (type === "overview") {
        const response = await StrapiClient.getPayoutsOverview(token);
        return res.status(200).json(response);
      } else {
        // Build query string for pagination and filtering
        const queryString = qs.stringify(queryParams, { encodeValuesOnly: true });
        const response = await StrapiClient.getPayouts(queryString, token);
        return res.status(200).json(response);
      }
    } catch (error: any) {
      console.error("Error fetching payouts:", error);
      sendApiError(res, error, "Error fetching payouts");
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
