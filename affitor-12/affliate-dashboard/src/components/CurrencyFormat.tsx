import React from 'react';
import { formatCurrency, formatCurrencyCompact, formatEPU, formatCommission, formatRevenue, CurrencyFormatOptions } from '@/utils/currency';

interface CurrencyFormatProps {
  value: number | null | undefined;
  type?: 'standard' | 'compact' | 'epu' | 'commission' | 'revenue';
  options?: CurrencyFormatOptions;
  className?: string;
}

/**
 * CurrencyFormat component for consistent currency display across the application
 * 
 * @param value - The numeric value to format
 * @param type - The type of currency formatting to apply
 * @param options - Additional formatting options
 * @param className - CSS classes to apply to the span element
 */
export const CurrencyFormat: React.FC<CurrencyFormatProps> = ({
  value,
  type = 'standard',
  options = {},
  className = ''
}) => {
  const formatValue = () => {
    switch (type) {
      case 'compact':
        return formatCurrencyCompact(value, options);
      case 'epu':
        return formatEPU(value, options);
      case 'commission':
        return formatCommission(value, options);
      case 'revenue':
        return formatRevenue(value, options);
      case 'standard':
      default:
        return formatCurrency(value, options);
    }
  };

  return (
    <span className={className} title={value ? `Exact value: ${formatCurrency(value)}` : undefined}>
      {formatValue()}
    </span>
  );
};

// Convenience components for specific use cases
export const EPUFormat: React.FC<Omit<CurrencyFormatProps, 'type'>> = (props) => (
  <CurrencyFormat {...props} type="epu" />
);

export const CommissionFormat: React.FC<Omit<CurrencyFormatProps, 'type'>> = (props) => (
  <CurrencyFormat {...props} type="commission" />
);

export const RevenueFormat: React.FC<Omit<CurrencyFormatProps, 'type'>> = (props) => (
  <CurrencyFormat {...props} type="revenue" />
);

export const CompactCurrencyFormat: React.FC<Omit<CurrencyFormatProps, 'type'>> = (props) => (
  <CurrencyFormat {...props} type="compact" />
);
