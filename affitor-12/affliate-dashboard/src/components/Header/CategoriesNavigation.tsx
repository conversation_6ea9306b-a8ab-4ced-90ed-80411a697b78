"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { ChevronDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface MinimalCategory {
  id: number;
  documentId: string;
  name: string;
  slug: string;
}

interface CategoriesNavigationProps {
  activeNav?: string;
  setActiveNav?: (nav: string) => void;
  className?: string;
}

export default function CategoriesNavigation({
  activeNav,
  setActiveNav,
  className
}: CategoriesNavigationProps) {
  const [categories, setCategories] = useState<MinimalCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch minimal categories data
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/categories/minimal');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch categories: ${response.status}`);
        }
        
        const data = await response.json();
        setCategories(data || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err instanceof Error ? err.message : 'Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryClick = (categoryName: string) => {
    if (setActiveNav) {
      setActiveNav(categoryName);
    }
    setIsOpen(false);
  };

  const isActive = activeNav === "Categories";

  return (
    <div className={cn("relative group z-10", className)}>
      <button
        className={cn(
          "flex items-center gap-1 text-primary-foreground hover:text-primary-foreground transition-colors font-medium",
          isActive && "font-semibold"
        )}
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        Categories
        <ChevronDownIcon 
          className={cn(
            "h-4 w-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )} 
        />
      </button>

      {/* Dropdown Menu */}
      <div
        className={cn(
          "absolute top-full left-0 w-48 rounded-lg shadow-lg bg-white border border-gray-200 transform transition-all duration-300 ease-in-out",
          isOpen 
            ? "visible opacity-100 translate-y-0" 
            : "invisible opacity-0 translate-y-4"
        )}
        onMouseLeave={() => setIsOpen(false)}
      >
        <div className="py-2">
          {/* Loading State */}
          {isLoading && (
            <div className="px-4 py-3 text-sm text-gray-500 flex items-center gap-2">
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              Loading categories...
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="px-4 py-3 text-sm text-red-500">
              {error}
            </div>
          )}

          {/* Categories List */}
          {!isLoading && !error && categories.length > 0 && (
            <>
              {categories.map((category) => (
                <Link
                  key={category.documentId}
                  href={`/categories/${category.slug}`}
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
                  onClick={() => handleCategoryClick(category.name)}
                >
                  {category.name}
                </Link>
              ))}
            </>
          )}

          {/* Empty State */}
          {!isLoading && !error && categories.length === 0 && (
            <div className="px-4 py-3 text-sm text-gray-500">
              No categories available
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
