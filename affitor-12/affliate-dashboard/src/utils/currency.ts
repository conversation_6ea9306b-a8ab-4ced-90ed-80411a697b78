/**
 * Currency formatting utilities for consistent monetary value display
 */

export interface CurrencyFormatOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  notation?: 'standard' | 'compact';
  compactDisplay?: 'short' | 'long';
}

/**
 * Format a number as currency with proper locale formatting
 * @param value - The numeric value to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | null | undefined,
  options: CurrencyFormatOptions = {}
): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '$0.00';
  }

  const {
    currency = 'USD',
    locale = 'en-US',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    notation = 'standard',
    compactDisplay = 'short'
  } = options;

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits,
      maximumFractionDigits,
      notation,
      compactDisplay
    }).format(value);
  } catch (error) {
    // Fallback for unsupported locales or currencies
    console.warn('Currency formatting failed, using fallback:', error);
    return `$${value.toLocaleString('en-US', {
      minimumFractionDigits,
      maximumFractionDigits
    })}`;
  }
}

/**
 * Format currency with compact notation for large numbers (e.g., $1.2M, $345.6K)
 * @param value - The numeric value to format
 * @param options - Formatting options
 * @returns Formatted compact currency string
 */
export function formatCurrencyCompact(
  value: number | null | undefined,
  options: Omit<CurrencyFormatOptions, 'notation'> = {}
): string {
  return formatCurrency(value, {
    ...options,
    notation: 'compact',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  });
}

/**
 * Format currency for EPU (Earnings Per User) values
 * Automatically chooses between standard and compact notation based on value size
 * @param value - The EPU value to format
 * @param options - Formatting options
 * @returns Formatted EPU currency string
 */
export function formatEPU(
  value: number | null | undefined,
  options: CurrencyFormatOptions = {}
): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '$0.00';
  }

  // Use compact notation for values >= 10,000
  if (value >= 10000) {
    return formatCurrencyCompact(value, options);
  }

  // Use standard notation with 2 decimal places for smaller values
  return formatCurrency(value, {
    ...options,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

/**
 * Format currency for commission amounts
 * @param value - The commission value to format
 * @param options - Formatting options
 * @returns Formatted commission currency string
 */
export function formatCommission(
  value: number | null | undefined,
  options: CurrencyFormatOptions = {}
): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '$0.00';
  }

  // Use compact notation for large commission values
  if (value >= 1000) {
    return formatCurrencyCompact(value, options);
  }

  return formatCurrency(value, {
    ...options,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

/**
 * Format revenue values (monthly traffic revenue, etc.)
 * @param value - The revenue value to format
 * @param options - Formatting options
 * @returns Formatted revenue currency string
 */
export function formatRevenue(
  value: number | null | undefined,
  options: CurrencyFormatOptions = {}
): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '$0';
  }

  // Always use compact notation for revenue values
  return formatCurrencyCompact(value, {
    ...options,
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  });
}

/**
 * Parse a currency string back to a number
 * @param currencyString - The currency string to parse
 * @returns Parsed number or null if invalid
 */
export function parseCurrency(currencyString: string): number | null {
  if (!currencyString || typeof currencyString !== 'string') {
    return null;
  }

  // Remove currency symbols, commas, and spaces
  const cleanedString = currencyString
    .replace(/[$€£¥₹,\s]/g, '')
    .replace(/[KMB]/i, (match) => {
      const multipliers = { K: 1000, M: 1000000, B: 1000000000 };
      return '';
    });

  const number = parseFloat(cleanedString);
  
  if (isNaN(number)) {
    return null;
  }

  // Handle K, M, B suffixes
  const suffix = currencyString.match(/[KMB]/i)?.[0]?.toUpperCase();
  if (suffix) {
    const multipliers = { K: 1000, M: 1000000, B: 1000000000 };
    return number * multipliers[suffix as keyof typeof multipliers];
  }

  return number;
}

/**
 * Format a percentage value
 * @param value - The percentage value (as decimal, e.g., 0.15 for 15%)
 * @param options - Formatting options
 * @returns Formatted percentage string
 */
export function formatPercentage(
  value: number | null | undefined,
  options: { minimumFractionDigits?: number; maximumFractionDigits?: number } = {}
): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0%';
  }

  const { minimumFractionDigits = 1, maximumFractionDigits = 2 } = options;

  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits,
    maximumFractionDigits
  }).format(value);
}
