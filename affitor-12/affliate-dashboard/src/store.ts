import { configureStore } from "@reduxjs/toolkit";
import { reducer as affiliateReducer } from "@features/affiliate/affiliate.slice";
import { reducer as categoryReducer } from "@features/category/category.slice";
import categoriesOverviewReducer from "@features/categories-overview/categories-overview.slice";
import categoryDetailReducer from "@features/category-detail/category-detail.slice";
import { reducer as socialListeningReducer } from "@features/social-listening/social-listening.slice";
import { reducer as trafficWebReducer } from "@features/traffic-web/traffic-web.slice";
import { reducer as authReducer } from "@features/auth/auth.slice";
import { aiscriptReducer } from "@features/aiscript/aiscript.slice";
import { reducer as paymentMethodReducer } from "@features/payment-method/payment-method.slice";
import { reducer as filterReducer } from "./features/filter/filter.slice";
import { reducer as userReducer } from "@features/user/user.slice";
import { reducer as subscriptionTierReducer } from "@features/subscription-tier/subscription-tier.slice";
import { reducer as topVideosReducer } from "@features/top-videos/top-videos.slice";
import { reducer as topAdsReducer } from "@features/top-ads/top-ads.slice";
import referrerReducer from "@features/referrer/referrer.slice";
import { reducer as referrerLinksReducer } from "@features/referrer-links/referrer-links.slice";
import { reducer as trackLinksReducer } from "@features/track-links/track-links.slice";
import { reducer as referralActivityReducer } from "@features/referral-activity/referral-activity.slice";
import { reducer as referralCommissionReducer } from "@features/referral-commission/referral-commission.slice";
import { reducer as referralReducer } from "@features/referral/referral.slice";
import { reducer as payoutReducer } from "@features/payout/payout.slice";
import adminReducer from "@features/admin/admin.slice";
import { reducer as spyheroReducer } from "@features/spyhero/spyhero.slice";
import { reducer as transactionReducer } from "@features/transaction/transaction.slice";
import { reducer as discourseReducer } from "@features/discourse/discourse.slice";
import pageReducer from "@features/page/page.slice";
import rootSaga from "@features/rootSaga";
import createSagaMiddleware from "redux-saga";
import { createWrapper } from "next-redux-wrapper";

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    affiliate: affiliateReducer,
    category: categoryReducer,
    categoriesOverview: categoriesOverviewReducer,
    categoryDetail: categoryDetailReducer,
    socialListening: socialListeningReducer,
    trafficWeb: trafficWebReducer,
    auth: authReducer,
    aiscript: aiscriptReducer,
    paymentMethod: paymentMethodReducer,
    filter: filterReducer,
    user: userReducer,
    subscriptionTier: subscriptionTierReducer,
    topVideos: topVideosReducer,
    topAds: topAdsReducer,
    referrer: referrerReducer,
    referrerLinks: referrerLinksReducer,
    trackLinks: trackLinksReducer,
    referralActivity: referralActivityReducer,
    referralCommission: referralCommissionReducer,
    referral: referralReducer,
    payout: payoutReducer,
    admin: adminReducer,
    spyhero: spyheroReducer,
    transaction: transactionReducer,
    discourse: discourseReducer,
    page: pageReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(sagaMiddleware),
});

sagaMiddleware.run(rootSaga);

// Infer the RootState and AppDispatch types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
const makeStore = () => store;

export const wrapper = createWrapper(makeStore);
