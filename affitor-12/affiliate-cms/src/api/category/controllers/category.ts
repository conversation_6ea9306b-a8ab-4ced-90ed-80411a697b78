/**
 *  category controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::category.category', ({ strapi }) => ({
  // Override default find method to include affiliates (for backward compatibility)
  async find(ctx) {
    try {
      // Get query parameters
      const { query } = ctx;

      // Build the query with minimal affiliate data
      const entities = await strapi.documents('api::category.category').findMany({
        ...query,
        populate: {
          affiliates: {
            fields: ['name', 'slug', 'monthly_traffic', 'avg_conversion', 'avg_price', 'domain'],
            populate: {
              image: {
                fields: ['url', 'alternativeText']
              }
            }
          }
        }
      });

      return entities;
    } catch (err) {
      console.error('Error in find categories:', err);
      ctx.throw(500, 'Failed to fetch categories');
    }
  },

  // Minimal categories endpoint for homepage/header filtering
  async findMinimal(ctx) {
    try {
      const entities = await strapi.documents('api::category.category').findMany({
        fields: ['name', 'slug'],
        sort: { name: 'asc' }
      });

      return entities;
    } catch (err) {
      console.error('Error in findMinimal categories:', err);
      ctx.throw(500, 'Failed to fetch minimal categories');
    }
  },

  // Get categories overview with metrics (optimized)
  async getCategoriesOverview(ctx) {
    try {
      const { search, sort } = ctx.query;

      // Get categories with minimal affiliate data
      const categories = await strapi.documents('api::category.category').findMany({
        populate: {
          affiliates: {
            fields: ['name', 'slug', 'monthly_traffic', 'avg_conversion', 'avg_price', 'domain'],
            populate: {
              image: {
                fields: ['url', 'alternativeText']
              }
            }
          }
        }
      });

      // Calculate metrics for each category
      const categoriesWithMetrics = await Promise.all(
        categories.map(async (category) => {
          const affiliates = category.affiliates || [];

          // Calculate average EPU
          const validEPUs = affiliates
            .filter(affiliate => affiliate.avg_conversion && affiliate.avg_price)
            .map(affiliate => affiliate.avg_conversion * affiliate.avg_price);
          const avgEPU = validEPUs.length > 0
            ? validEPUs.reduce((sum, epu) => sum + epu, 0) / validEPUs.length
            : 0;

          // Get top 3 programs by performance score
          const affiliatesWithScore = affiliates.map(affiliate => {
            const epu = (affiliate.avg_conversion || 0) * (affiliate.avg_price || 0);
            const traffic = Number(affiliate.monthly_traffic) || 0;
            const trafficScore = Math.log10(traffic + 1);
            return { ...affiliate, epu, performanceScore: epu * trafficScore, traffic };
          });

          const topPrograms = affiliatesWithScore
            .filter(affiliate => affiliate.traffic > 0)
            .sort((a, b) => b.performanceScore - a.performanceScore)
            .slice(0, 3)
            .map(affiliate => ({
              id: affiliate.documentId,
              name: affiliate.name,
              image: affiliate.image,
              epu: Math.round(affiliate.epu * 100) / 100,
              commission: 'N/A',
              monthlyTraffic: affiliate.traffic.toString()
            }));

          // Get top videos (simplified for now)
          const topVideos = [];

          // Calculate totals
          const totalPrograms = affiliates.length;
          const validTraffic = affiliates
            .filter(affiliate => affiliate.monthly_traffic && Number(affiliate.monthly_traffic) > 0)
            .map(affiliate => Number(affiliate.monthly_traffic));
          const avgMonthlyTraffic = validTraffic.length > 0
            ? validTraffic.reduce((sum, traffic) => sum + traffic, 0) / validTraffic.length
            : 0;

          return {
            ...category,
            avgEPU: Math.round(avgEPU * 100) / 100,
            topPrograms,
            topVideos,
            totalPrograms,
            totalVideos: 0, // Will be populated later if needed
            totalViews: 0,
            avgMonthlyTraffic: Math.round(avgMonthlyTraffic)
          };
        })
      );

      ctx.body = {
        data: categoriesWithMetrics,
        meta: { total: categoriesWithMetrics.length }
      };
    } catch (err) {
      console.error('Error in getCategoriesOverview controller:', err);
      ctx.throw(500, 'Failed to fetch categories overview');
    }
  },

  // Get category detail with summary metrics
  async getCategoryDetail(ctx) {
    try {
      const { slug } = ctx.params;

      const result = await strapi
        .service('api::category.category')
        .getCategoryDetail(slug);

      if (!result) {
        ctx.throw(404, 'Category not found');
      }

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategoryDetail:', err);
      if (err.status === 404) {
        ctx.throw(404, err.message);
      }
      ctx.throw(500, 'Failed to fetch category detail');
    }
  },

  // Get category programs with filtering and sorting
  async getCategoryPrograms(ctx) {
    try {
      const { slug } = ctx.params;
      const { search, sort, pagination } = ctx.query;

      const result = await strapi
        .service('api::category.category')
        .getCategoryPrograms(slug, { search, sort, pagination });

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategoryPrograms:', err);
      ctx.throw(500, 'Failed to fetch category programs');
    }
  },

  // Get category social content
  async getCategorySocialContent(ctx) {
    try {
      const { slug } = ctx.params;
      const { platforms, sort, pagination } = ctx.query;

      const result = await strapi
        .service('api::category.category')
        .getCategorySocialContent(slug, { platforms, sort, pagination });

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategorySocialContent:', err);
      ctx.throw(500, 'Failed to fetch category social content');
    }
  },

  // Utility function to assign affiliates to categories (for testing)
  async assignAffiliatesToCategories(ctx) {
    try {
      // Get all categories and affiliates
      const categories = await strapi.documents('api::category.category').findMany({});
      const affiliates = await strapi.documents('api::affiliate.affiliate').findMany({});

      if (!categories || !affiliates || categories.length === 0 || affiliates.length === 0) {
        return ctx.send({ message: 'No categories or affiliates found' });
      }

      // Simple assignment logic - assign affiliates to categories based on keywords
      const assignments = [
        {
          categoryName: 'AI Agent1',
          affiliateKeywords: ['AI', 'AKOOL', 'AdCreative', 'AISEO', '1min AI', 'AI Detector']
        },
        {
          categoryName: 'Category1',
          affiliateKeywords: ['Nifty', 'BasedLabs']
        }
      ];

      let assignmentCount = 0;

      for (const assignment of assignments) {
        const category = categories.find(cat => cat.name === assignment.categoryName);
        if (!category) continue;

        const matchingAffiliates = affiliates.filter(affiliate =>
          assignment.affiliateKeywords.some(keyword =>
            affiliate.name.toLowerCase().includes(keyword.toLowerCase())
          )
        );

        if (matchingAffiliates.length > 0) {
          // Update category with affiliates
          await strapi.documents('api::category.category').update({
            documentId: category.documentId,
            data: {
              affiliates: matchingAffiliates.map(aff => aff.documentId)
            }
          });
          assignmentCount += matchingAffiliates.length;
        }
      }

      return ctx.send({
        message: `Successfully assigned ${assignmentCount} affiliates to categories`,
        assignments: assignmentCount
      });
    } catch (err) {
      console.error('Error in assignAffiliatesToCategories:', err);
      ctx.throw(500, 'Failed to assign affiliates to categories');
    }
  },
}));
