export default {
  routes: [
    {
      method: 'GET',
      path: '/categories/minimal',
      handler: 'category.findMinimal',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/overview',
      handler: 'category.getCategoriesOverview',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/detail',
      handler: 'category.getCategoryDetail',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/programs',
      handler: 'category.getCategoryPrograms',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/social-content',
      handler: 'category.getCategorySocialContent',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/categories/assign-affiliates',
      handler: 'category.assignAffiliatesToCategories',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
  ],
};
