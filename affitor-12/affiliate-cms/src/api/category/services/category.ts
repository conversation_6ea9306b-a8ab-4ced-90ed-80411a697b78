/**
 * category service.
 */

import { factories } from '@strapi/strapi';
import { IAffiliate } from '../../../api/affiliate/interfaces';

export default factories.createCoreService('api::category.category', ({ strapi }) => ({
  // Get categories overview with metrics (optimized)
  async getCategoriesOverview({ search, sort }) {
    try {
      console.log('Starting getCategoriesOverview with params:', { search, sort });

      // Build filters for search
      const filters: any = {};
      if (search) {
        filters.$or = [
          { name: { $containsi: search } },
          { description: { $containsi: search } }
        ];
      }

      console.log('Fetching categories with filters:', filters);

      // Get categories with minimal affiliate data needed for calculations
      const categories = await strapi.documents('api::category.category').findMany({
        filters,
        populate: {
          affiliates: {
            fields: ['name', 'slug', 'monthly_traffic', 'avg_conversion', 'avg_price', 'domain', 'launch_year'],
            populate: {
              image: {
                fields: ['url', 'alternativeText']
              }
            }
          }
        }
      });

      console.log('Found categories:', categories?.length || 0);

      // Calculate metrics for each category
      const categoriesWithMetrics = await Promise.all(
        categories.map(async (category) => {
          console.log('Calculating metrics for category:', category.name);
          const metrics = await this.calculateCategoryMetricsOptimized(category);
          return {
            ...category,
            ...metrics
          };
        })
      );

      console.log('Calculated metrics for all categories');

      // Apply sorting
      if (sort) {
        categoriesWithMetrics.sort((a, b) => {
          const [field, order] = sort.split(':');
          const aValue = a[field] || 0;
          const bValue = b[field] || 0;

          if (order === 'desc') {
            return bValue - aValue;
          }
          return aValue - bValue;
        });
      }

      return {
        data: categoriesWithMetrics,
        meta: {
          total: categoriesWithMetrics.length
        }
      };
    } catch (error) {
      console.error('Error in getCategoriesOverview service:', error);
      throw error;
    }
  },



  // Optimized method that uses database-level queries for top programs
  async calculateCategoryMetricsOptimized(category) {
    const categoryId = category.id || category.documentId;

    if (!categoryId) {
      return {
        avgEPU: 0,
        topPrograms: [],
        topVideos: [],
        totalPrograms: 0,
        totalVideos: 0,
        totalViews: 0,
        avgMonthlyTraffic: 0
      };
    }

    try {
      // Get basic category stats and top programs in parallel
      const [categoryStats, topProgramsData, topVideosData, videoStats] = await Promise.all([
        // Get category statistics
        this.getCategoryStatistics(categoryId),

        // Get top 3 programs using database query with performance score calculation
        this.getTopProgramsByPerformance(categoryId, 3),

        // Get top videos
        this.getTopVideosByCategory(categoryId, 3),

        // Get total video count
        this.getTotalVideosByCategory(categoryId)
      ]);

      return {
        avgEPU: categoryStats.avgEPU,
        topPrograms: topProgramsData,
        topVideos: topVideosData,
        totalPrograms: categoryStats.totalPrograms,
        totalVideos: videoStats,
        totalViews: categoryStats.totalViews,
        avgMonthlyTraffic: categoryStats.avgMonthlyTraffic
      };
    } catch (error) {
      console.error('Error in calculateCategoryMetricsOptimized:', error);
      // Fallback to original method
      return this.calculateCategoryMetricsOriginal(category);
    }
  },

  // Get category statistics using aggregation queries
  async getCategoryStatistics(categoryId) {
    try {
      // Use raw SQL query for better performance with aggregations
      const result = await strapi.db.connection.raw(`
        SELECT
          COUNT(*) as total_programs,
          AVG(CASE
            WHEN avg_conversion IS NOT NULL AND avg_price IS NOT NULL
            THEN avg_conversion * avg_price
            ELSE NULL
          END) as avg_epu,
          AVG(CASE
            WHEN monthly_traffic IS NOT NULL AND monthly_traffic > 0
            THEN CAST(monthly_traffic AS DECIMAL)
            ELSE NULL
          END) as avg_monthly_traffic
        FROM affiliates a
        INNER JOIN affiliates_categories_lnk ac ON a.id = ac.affiliate_id
        WHERE ac.category_id = ?
      `, [categoryId]);

      const stats = result[0] || {};

      // Get total views from social content
      const viewsResult = await strapi.db.connection.raw(`
        SELECT COALESCE(SUM(CAST(views AS DECIMAL)), 0) as total_views
        FROM social_listenings sl
        INNER JOIN affiliates a ON sl.affiliate_id = a.id
        INNER JOIN affiliates_categories_lnk ac ON a.id = ac.affiliate_id
        WHERE ac.category_id = ?
      `, [categoryId]);

      return {
        totalPrograms: parseInt(stats.total_programs) || 0,
        avgEPU: Math.round((parseFloat(stats.avg_epu) || 0) * 100) / 100,
        avgMonthlyTraffic: Math.round(parseFloat(stats.avg_monthly_traffic) || 0),
        totalViews: parseInt(viewsResult[0]?.total_views) || 0
      };
    } catch (error) {
      console.error('Error in getCategoryStatistics:', error);
      return {
        totalPrograms: 0,
        avgEPU: 0,
        avgMonthlyTraffic: 0,
        totalViews: 0
      };
    }
  },

  // Get top programs by performance score using database query
  async getTopProgramsByPerformance(categoryId, limit = 3) {
    try {
      // Use raw SQL for complex performance score calculation
      const result = await strapi.db.connection.raw(`
        SELECT
          a.document_id,
          a.name,
          a.avg_conversion,
          a.avg_price,
          a.monthly_traffic,
          (COALESCE(a.avg_conversion, 0) * COALESCE(a.avg_price, 0)) as epu,
          (COALESCE(a.avg_conversion, 0) * COALESCE(a.avg_price, 0) *
           LOG10(COALESCE(CAST(a.monthly_traffic AS DECIMAL), 1) + 1)) as performance_score
        FROM affiliates a
        INNER JOIN affiliates_categories_lnk ac ON a.id = ac.affiliate_id
        WHERE ac.category_id = ?
          AND a.monthly_traffic IS NOT NULL
          AND CAST(a.monthly_traffic AS DECIMAL) > 0
          AND a.avg_conversion IS NOT NULL
          AND a.avg_price IS NOT NULL
        ORDER BY performance_score DESC
        LIMIT ?
      `, [categoryId, limit]);

      // Get images for the top programs
      const programIds = result.map(r => r.document_id);
      const images = await strapi.documents('api::affiliate.affiliate').findMany({
        filters: { documentId: { $in: programIds } },
        fields: ['documentId'],
        populate: {
          image: {
            fields: ['url', 'alternativeText']
          }
        }
      });

      const imageMap = new Map();
      images.forEach(affiliate => {
        imageMap.set(affiliate.documentId, affiliate.image);
      });

      return result.map(program => ({
        id: program.document_id,
        name: program.name,
        image: imageMap.get(program.document_id),
        epu: Math.round(parseFloat(program.epu) * 100) / 100,
        commission: 'N/A', // Will be populated from commission data if needed
        monthlyTraffic: program.monthly_traffic?.toString() || '0'
      }));
    } catch (error) {
      console.error('Error in getTopProgramsByPerformance:', error);
      return [];
    }
  },

  // Get top videos by category
  async getTopVideosByCategory(categoryId, limit = 3) {
    try {
      const topVideosData = await strapi.documents('api::social-listening.social-listening').findMany({
        filters: {
          affiliate: {
            categories: {
              id: { $eq: categoryId }
            }
          }
        },
        sort: { views: 'desc' },
        limit,
        fields: ['title', 'thumbnail', 'views', 'platform', 'link'],
        populate: {
          affiliate: {
            fields: ['name']
          }
        }
      });

      return topVideosData.map(video => ({
        id: video.documentId,
        title: video.title || '',
        thumbnail: video.thumbnail || '',
        views: Number(video.views) || 0,
        platform: video.platform || '',
        link: video.link || '',
        affiliate: video.affiliate?.name || ''
      }));
    } catch (error) {
      console.error('Error in getTopVideosByCategory:', error);
      return [];
    }
  },

  // Get total videos count by category
  async getTotalVideosByCategory(categoryId) {
    try {
      return await strapi.db.query('api::social-listening.social-listening').count({
        where: {
          affiliate: {
            categories: {
              id: categoryId
            }
          }
        }
      });
    } catch (error) {
      console.error('Error in getTotalVideosByCategory:', error);
      return 0;
    }
  },

  // Main method that delegates to optimized version
  async calculateCategoryMetrics(category) {
    return this.calculateCategoryMetricsOptimized(category);
  },

  // Original method for backward compatibility and fallback
  async calculateCategoryMetricsOriginal(category) {
    const affiliates = category.affiliates || [];

    if (affiliates.length === 0) {
      return {
        avgEPU: 0,
        topPrograms: [],
        topVideos: [],
        totalPrograms: 0,
        totalVideos: 0,
        totalViews: 0,
        avgMonthlyTraffic: 0
      };
    }

    // Calculate average EPU based on avg_conversion and avg_price
    const validEPUs = affiliates
      .filter(affiliate => affiliate.avg_conversion && affiliate.avg_price)
      .map(affiliate => affiliate.avg_conversion * affiliate.avg_price);

    const avgEPU = validEPUs.length > 0
      ? validEPUs.reduce((sum, epu) => sum + epu, 0) / validEPUs.length
      : 0;

    // Calculate performance score for ranking (EPU * monthly_traffic weight)
    const affiliatesWithScore = affiliates.map(affiliate => {
      const epu = (affiliate.avg_conversion || 0) * (affiliate.avg_price || 0);
      const trafficScore = Math.log10((affiliate.monthly_traffic || 1) + 1); // Log scale for traffic
      const performanceScore = epu * trafficScore;

      return {
        ...affiliate,
        epu,
        performanceScore
      };
    });

    // Get top 3 programs by performance score (EPU + traffic combination)
    const topPrograms = affiliatesWithScore
      .filter(affiliate => affiliate.monthly_traffic > 0)
      .sort((a, b) => b.performanceScore - a.performanceScore)
      .slice(0, 3)
      .map(affiliate => ({
        id: affiliate.documentId,
        name: affiliate.name,
        image: affiliate.image,
        epu: Math.round(affiliate.epu * 100) / 100,
        commission: 'N/A', // Will be populated from commission data if needed
        monthlyTraffic: affiliate.monthly_traffic.toString()
      }));

    // Get social content data for top videos (optimized query)
    const affiliateIds = affiliates.map(affiliate => affiliate.documentId);

    // Single query to get top videos and total counts
    const [topVideosData, videoStats] = await Promise.all([
      // Top 3 videos by views
      strapi.documents('api::social-listening.social-listening').findMany({
        filters: {
          affiliate: { documentId: { $in: affiliateIds } }
        },
        sort: { views: 'desc' },
        limit: 3,
        fields: ['title', 'thumbnail', 'views', 'platform', 'link'],
        populate: {
          affiliate: {
            fields: ['name']
          }
        }
      }),
      // Get total counts in a single aggregation query
      strapi.db.query('api::social-listening.social-listening').count({
        where: {
          affiliate: { id: { $in: affiliateIds } }
        }
      })
    ]);

    const topVideos = topVideosData.map(video => ({
      id: video.documentId,
      title: video.title || '',
      thumbnail: video.thumbnail || '',
      views: Number(video.views) || 0,
      platform: video.platform || '',
      link: video.link || '',
      affiliate: video.affiliate?.name || ''
    }));

    // Calculate total views from top videos (approximation for performance)
    const totalViews = topVideosData.reduce((sum, video) => sum + (Number(video.views) || 0), 0);

    // Calculate average monthly traffic
    const validTraffic = affiliates
      .filter(affiliate => affiliate.monthly_traffic && affiliate.monthly_traffic > 0)
      .map(affiliate => Number(affiliate.monthly_traffic));

    const avgMonthlyTraffic = validTraffic.length > 0
      ? validTraffic.reduce((sum, traffic) => sum + traffic, 0) / validTraffic.length
      : 0;

    return {
      avgEPU: Math.round(avgEPU * 100) / 100,
      topPrograms,
      topVideos,
      totalPrograms: affiliates.length,
      totalVideos: videoStats || 0,
      totalViews,
      avgMonthlyTraffic: Math.round(avgMonthlyTraffic)
    };
  },

  // Get category detail
  async getCategoryDetail(slug) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug },
        populate: {
          affiliates: {
            populate: {
              commission: true,
              social_logs: true,
            }
          }
        }
      });

      if (!category) {
        return null;
      }

      const metrics = await this.calculateCategoryMetrics(category);

      return {
        ...category,
        ...metrics
      };
    } catch (error) {
      console.error('Error in getCategoryDetail service:', error);
      throw error;
    }
  },

  // Get category programs with filtering and sorting
  async getCategoryPrograms(slug, { search, sort, pagination = { page: 1, pageSize: 10 } }) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug }
      });

      if (!category) {
        throw new Error('Category not found');
      }

      // Build filters
      const filters: any = {
        categories: { documentId: category.documentId }
      };

      if (search) {
        filters.$or = [
          { name: { $containsi: search } },
          { company_name: { $containsi: search } }
        ];
      }

      // Build sort
      const sortOptions: any = {};
      if (sort) {
        const [field, order] = sort.split(':');
        sortOptions[field] = order;
      } else {
        sortOptions.name = 'asc'; // default sort by name
      }

      const affiliatesResult = await strapi.documents('api::affiliate.affiliate').findMany({
        filters,
        sort: sortOptions,
        pagination,
        populate: {
          commission: true,
          image: true,
          categories: true,
          social_logs: true
        }
      });

      // Type assertion for the result structure
      const typedAffiliatesResult = affiliatesResult as unknown as {
        data: Array<IAffiliate & {
          id: number;
          documentId: string;
        }>;
        meta: {
          pagination: {
            page: number;
            pageSize: number;
            pageCount: number;
            total: number;
          };
        };
      };

      // Get social content counts for each affiliate
      const affiliatesWithMetrics = await Promise.all(
        typedAffiliatesResult.data.map(async (affiliate) => {
          const socialContent = await strapi.documents('api::social-listening.social-listening').findMany({
            filters: {
              affiliate: { documentId: affiliate.documentId }
            }
          });

          const totalVideos = socialContent.length;
          const totalViews = socialContent.reduce((sum, content) => sum + (Number(content.views) || 0), 0);

          return {
            ...affiliate,
            totalVideos,
            totalViews
          };
        })
      );

      return {
        data: affiliatesWithMetrics,
        meta: typedAffiliatesResult.meta
      };
    } catch (error) {
      console.error('Error in getCategoryPrograms service:', error);
      throw error;
    }
  },

  // Get category social content
  async getCategorySocialContent(slug, { platforms, sort, pagination = { page: 1, pageSize: 10 } }) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug },
        populate: {
          affiliates: true
        }
      });

      if (!category) {
        throw new Error('Category not found');
      }

      const affiliateIds = category.affiliates.map(affiliate => affiliate.documentId);

      // Build filters
      const filters: any = {
        affiliate: { documentId: { $in: affiliateIds } }
      };

      if (platforms && platforms.length > 0) {
        filters.platform = { $in: platforms };
      }

      // Build sort
      const sortOptions: any = {};
      if (sort) {
        const [field, order] = sort.split(':');
        sortOptions[field] = order;
      } else {
        sortOptions.views = 'desc'; // default sort by views
      }

      const socialContent = await strapi.documents('api::social-listening.social-listening').findMany({
        filters,
        sort: sortOptions,
        pagination,
        populate: {
          affiliate: true
        }
      });

      return socialContent;
    } catch (error) {
      console.error('Error in getCategorySocialContent service:', error);
      throw error;
    }
  }
}));
