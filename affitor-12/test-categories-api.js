#!/usr/bin/env node

/**
 * Simple test script to verify the categories API endpoints
 * Run with: node test-categories-api.js
 */

const BASE_URL = 'http://localhost:1337';

async function testAPI(endpoint, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📡 Endpoint: ${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`);
    
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success! Data keys: ${Object.keys(data).join(', ')}`);
      
      if (data.data && Array.isArray(data.data)) {
        console.log(`📈 Found ${data.data.length} items`);
        if (data.data.length > 0) {
          console.log(`🔍 First item keys: ${Object.keys(data.data[0]).join(', ')}`);
        }
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
    }
  } catch (error) {
    console.log(`💥 Request failed: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Starting Categories API Tests');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  
  // Test categories overview
  await testAPI('/api/categories/overview', 'Categories Overview');
  
  // Test regular categories endpoint
  await testAPI('/api/categories', 'Regular Categories');
  
  // Test category detail (assuming there's a category with slug 'ai-video')
  await testAPI('/api/categories/ai-video/detail', 'Category Detail (ai-video)');
  
  // Test category programs
  await testAPI('/api/categories/ai-video/programs', 'Category Programs (ai-video)');
  
  // Test category social content
  await testAPI('/api/categories/ai-video/social-content', 'Category Social Content (ai-video)');
  
  console.log('\n🏁 Tests completed!');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  console.log('💡 Alternatively, install node-fetch: npm install node-fetch');
  process.exit(1);
}

runTests().catch(console.error);
