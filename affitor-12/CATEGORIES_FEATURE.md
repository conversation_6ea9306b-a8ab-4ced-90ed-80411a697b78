# Categories Overview & Detail Feature

This document describes the new Categories Overview and Detail pages implementation for the affiliate marketing platform.

## Overview

The Categories feature provides users with:
1. **Categories Overview Page** - A comprehensive view of all affiliate program categories with key metrics
2. **Category Detail Page** - Detailed information about a specific category including programs and social content

## Features Implemented

### Categories Overview Page (`/categories`)

**Main Features:**
- Category List with clickable cards
- Key metrics per category:
  - Average EPU (Earnings Per Unit)
  - Top 3 Programs (by monthly views)
  - Top 3 Most-viewed Videos
  - Total Programs count
  - Total Videos count
  - Total Views aggregated
  - Average Monthly Traffic
- Search functionality (by category name or program name)
- Sorting by metrics (programs, videos, traffic, EPU)

**Components:**
- `CategoriesOverview/index.tsx` - Main container
- `CategoriesOverview/CategoryCard.tsx` - Individual category cards with hover previews

### Category Detail Page (`/categories/[slug]`)

**Main Features:**
- Category Summary Section with detailed metrics
- Program Metrics Section with searchable/sortable table
- Top Social Content Section with platform filtering
- Sidebar navigation for easy section jumping

**Components:**
- `CategoryDetail/index.tsx` - Main container with sidebar navigation
- `CategoryDetail/CategorySummary.tsx` - Overview metrics and top programs/videos
- `CategoryDetail/CategoryPrograms.tsx` - Searchable/sortable programs table
- `CategoryDetail/CategorySocialContent.tsx` - Social content grid with platform filters
- `CategoryDetail/CategorySidebar.tsx` - Section navigation

## Backend API Endpoints

### New Strapi Endpoints

1. **GET `/api/categories/overview`**
   - Returns all categories with calculated metrics
   - Supports search and sorting query parameters

2. **GET `/api/categories/:slug/detail`**
   - Returns detailed category information with metrics

3. **GET `/api/categories/:slug/programs`**
   - Returns paginated programs for a category
   - Supports search, sorting, and pagination

4. **GET `/api/categories/:slug/social-content`**
   - Returns paginated social content for a category
   - Supports platform filtering, sorting, and pagination

### Frontend API Routes

1. **GET `/api/categories/overview`** - Proxy to Strapi
2. **GET `/api/categories/[slug]/detail`** - Proxy to Strapi
3. **GET `/api/categories/[slug]/programs`** - Proxy to Strapi
4. **GET `/api/categories/[slug]/social-content`** - Proxy to Strapi

## Data Models

### ICategoryOverview Interface
```typescript
interface ICategoryOverview extends ICategory {
  avgEPU: number;
  topPrograms: ITopProgram[];
  topVideos: ITopVideo[];
  totalPrograms: number;
  totalVideos: number;
  totalViews: number;
  avgMonthlyTraffic: number;
}
```

### ITopProgram Interface
```typescript
interface ITopProgram {
  id: string;
  name: string;
  image?: any;
  epu: number;
  commission: string;
  monthlyTraffic: number;
}
```

### ITopVideo Interface
```typescript
interface ITopVideo {
  id: string;
  title: string;
  thumbnail: string;
  views: number;
  platform: string;
  link: string;
  affiliate: string;
}
```

## Redux State Management

### Categories Overview
- **Slice**: `features/categories-overview/categories-overview.slice.ts`
- **Saga**: `features/categories-overview/categories-overview.saga.ts`
- **State**: Search term, sort options, loading states, error handling

### Category Detail
- **Slice**: `features/category-detail/category-detail.slice.ts`
- **Saga**: `features/category-detail/category-detail.saga.ts`
- **State**: Category data, programs data, social content data, all with separate loading/error states

## Key Metrics Calculations

### Average EPU
- Calculated from commission data across all programs in category
- Uses `commission.avg_commission` field

### Top Programs
- Sorted by `monthly_traffic` field
- Limited to top 3 results

### Top Videos
- Sorted by `views` field from social-listening data
- Limited to top 3 results
- Filtered by `type: 'video'`

### Total Views/Videos
- Aggregated from all social-listening entries for programs in category

### Average Monthly Traffic
- Calculated from `monthly_traffic` field across all programs

## Navigation Flow

1. **Entry Points:**
   - Direct navigation to `/categories`
   - Category selector in header (existing)

2. **User Journey:**
   - Browse categories overview → Click category → View detailed category page
   - Search/filter categories → Select category → Explore programs and content
   - Use sidebar navigation within category detail page

## Testing

A test script is provided at `test-categories-api.js` to verify backend API endpoints:

```bash
node test-categories-api.js
```

## Future Enhancements

1. **Video Playback**: Implement video preview on hover in category cards
2. **Advanced Filtering**: Add more filter options (date ranges, performance metrics)
3. **Export Functionality**: Allow users to export category data
4. **Comparison Mode**: Compare multiple categories side-by-side
5. **Real-time Updates**: WebSocket integration for live metric updates

## Files Modified/Created

### Backend (Strapi)
- `affiliate-cms/src/api/category/routes/custom-api.ts` (new)
- `affiliate-cms/src/api/category/controllers/category.ts` (modified)
- `affiliate-cms/src/api/category/services/category.ts` (modified)

### Frontend API
- `affliate-dashboard/src/pages/api/categories/overview.ts` (new)
- `affliate-dashboard/src/pages/api/categories/[slug]/detail.ts` (new)
- `affliate-dashboard/src/pages/api/categories/[slug]/programs.ts` (new)
- `affliate-dashboard/src/pages/api/categories/[slug]/social-content.ts` (new)

### Frontend Components
- `affliate-dashboard/src/containers/CategoriesOverview/` (new directory)
- `affliate-dashboard/src/containers/CategoryDetail/` (new directory)
- `affliate-dashboard/src/features/categories-overview/` (new directory)
- `affliate-dashboard/src/features/category-detail/` (new directory)

### Frontend Pages
- `affliate-dashboard/src/pages/categories/index.tsx` (new)
- `affliate-dashboard/src/pages/categories/[category].tsx` (modified)

### Configuration
- `affliate-dashboard/src/store.ts` (modified)
- `affliate-dashboard/src/features/rootSaga.ts` (modified)
- `affliate-dashboard/src/features/selectors.ts` (modified)
- `affliate-dashboard/src/interfaces/index.ts` (modified)
- `affliate-dashboard/src/utils/request.ts` (modified)
- `affliate-dashboard/src/components/TableAffiliate.tsx` (modified)
